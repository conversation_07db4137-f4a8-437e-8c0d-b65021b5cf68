#!/bin/sh

set -e

# 部署环境
DEPLOY_ENV=$1
# 不同环境的docker tag名称, canary/prod是手动部署，采用的是release docker镜像
IMAGE_TAG=$2

# 更新 Deployment（如果存在）或创建新 Deployment
update_deploy() {
    local namespace=$1  # 目标命名空间（环境，如 prod/canary/dev）
    local kubeconfig=$2 # 对应的 kubeconfig 配置文件
    local deploy_name="${CI_PROJECT_NAMESPACE}-${CI_PROJECT_NAME}"
    local image_full="reg.shukeyun.com:9088/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:${IMAGE_TAG}"

    # 检查 Deployment 是否存在
    if kubectl -n "$namespace" --kubeconfig="$kubeconfig" get deploy "$deploy_name" >/dev/null 2>&1; then
        echo "Deployment [$deploy_name] exists, updating image..."

        # 检查并 patch imagePullPolicy
        # pull_policy=$(kubectl -n "$namespace" --kubeconfig="$kubeconfig" get deployment "$deploy_name" -o jsonpath='{.spec.template.spec.containers[0].imagePullPolicy}')

        # if [ "$pull_policy" != "IfNotPresent" ]; then
        # echo "⚠️  imagePullPolicy is $pull_policy, patching to IfNotPresent..."
        # kubectl -n "$namespace" --kubeconfig="$kubeconfig" patch deployment "$deploy_name" \
        #     --type='json' \
        #     -p='[{"op": "replace", "path": "/spec/template/spec/containers/0/imagePullPolicy", "value": "IfNotPresent"}]'
        # fi

        # 去镜像仓库拉取镜像
        kubectl -n "$namespace" --kubeconfig="$kubeconfig" set image deployment/"$deploy_name" \
            "$deploy_name"="$image_full"

        # 重启触发 Pod 重建
        #kubectl rollout restart deployment/$deploy_name -n "$namespace" --kubeconfig="$kubeconfig"

        # 等待 Deployment 更新完成（最多等待180秒）
        echo "Waiting for deployment to rollout..."
        if kubectl rollout status deployment/"$deploy_name" -n "$namespace" --timeout=180s --kubeconfig="$kubeconfig"; then
            echo "✅ Deployment [$deploy_name] rollout successful"
        else
            echo "❌ Deployment [$deploy_name] rollout failed"
            kubectl -n "$namespace" --kubeconfig="$kubeconfig" describe deployment "$deploy_name"
            exit 1
        fi
    else
        echo "Deployment [$deploy_name] not found, creating..."
        create_deploy "$namespace" "$kubeconfig"
    fi
}

# 使用 deploy.yaml 模板创建新的 Deployment
create_deploy() {
    local namespace=$1
    local kubeconfig=$2

    echo "Deployment does not exist, creating new deployment..."

    # 通过 sed 替换 deploy.yaml 模板中的占位符
    sed -i "s/{{PROJECT_NAME}}/$CI_PROJECT_NAME/g" deploy.yaml
    sed -i "s/{{NAMESPACE}}/$namespace/g" deploy.yaml
    sed -i "s/{{PROJECT_NAMESPACE}}/$CI_PROJECT_NAMESPACE/g" deploy.yaml
    sed -i "s/{{IMAGE_TAG}}/$IMAGE_TAG/g" deploy.yaml
    sed -i "s/{{SERVICE_NS}}/$namespace/g" deploy.yaml

    # 输出 deploy.yaml 以供调试
    cat deploy.yaml

    # 应用 deployment 资源
    kubectl -n "$namespace" --kubeconfig="$kubeconfig" apply -f deploy.yaml
}

# 生产环境（prod）部署 , 使用的是release/*的分支，手动部署
if [ "$DEPLOY_ENV" == "prod" ]; then
    update_deploy "prod" "/etc/deploy/kube2prod"

# 灰度环境（canary）部署, , 使用的是release/*的分支，手动部署
elif [ "$DEPLOY_ENV" == "canary" ]; then
    update_deploy "canary" "/etc/deploy/kube2canary"

# 测试环境（test）部署, 使用的是release/*的分支，自动部署
elif [ "$DEPLOY_ENV" == "test" ]; then
    update_deploy "test" "/etc/deploy/config"

# dev 部署，使用的是dev分支，自动部署
else
    update_deploy "dev" "/etc/deploy/config"
fi
