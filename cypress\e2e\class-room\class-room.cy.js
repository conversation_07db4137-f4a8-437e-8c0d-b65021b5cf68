describe('虚拟课程端到端测试', () => {
  before(() => {
    // cy.login(, 'admin123');

    cy.visit('https://dev.shukeyun.com/cas/login/#/login?appId=ieCx4PxQExAFrJafek9g');
    cy.get('div.white > div:nth-of-type(2) input').click();
    cy.get('div.white > div:nth-of-type(1) input').type('13715072883');
    cy.get('div.white > div:nth-of-type(2) input').type('admin123');
    cy.get('div.btn').click();
    cy.visit('https://dev.shukeyun.com/data/ai-platform-frontend/#/model');
    cy.url().should('include', '/#/'); // 等页面跳转到带 tk 的地址后再继续
  });

  it('tests 虚拟课程端到端测试', () => {
    cy.viewport(2032, 555);
    cy.visit('https://localhost:5173/#/train');
    // cy.get('li.ant-menu-item > span').click();
    // cy.get('input').click();
    // cy.get('input').type('20250611');
    // cy.get('#rc-tabs-1-tab-2').click();
    // cy.get('#rc-tabs-1-tab-3').click();
    // cy.get('button > span:nth-of-type(2)').first.click();
    // cy.get('#rc-tabs-2-tab-2').click();
  });
});
