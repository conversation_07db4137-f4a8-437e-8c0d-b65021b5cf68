# git hook提交代码时进行代码校验配置方法

## ✅ 1. 安装Husky v9

```
npm install --save-dev husky
```

---

## ✅ 2. 初始化<PERSON><PERSON> v9

```
npx husky init
```

这会自动：

- 创建 `.husky/` 目录
- 添加 `pre-commit` hook 示例
- 添加 `"prepare": "husky install"` 脚本

---

## ✅ 3. 配置 `package.json`

```
{
  "scripts": {
    "lint": "run-s lint:*",
    "lint:eslint":  "eslint src/**/*.{ts,tsx,vue}  --debug",
    "lint:prettier": "prettier --check ./",
    "lint:style":  "stylelint \"src/**/*.{vue,css,less}\" --fix",
  },
}
```

---

## ✅ 4. 修改 `.husky/pre-commit` hook

```
npm run lint
```

---

## ✅ 5. 测试是否生效

修改 `src` 下的某个 `.ts` / `.tsx` 文件，故意触发代码质量问题，然后：

```
git add .
git commit -m "Test husky v9"
```

会看到 ESLint 自动运行并格式化文件，**如果有未修复的错误，提交会被中断**。

![1745213330368](image/README/1745213330368.png)

测试一下
