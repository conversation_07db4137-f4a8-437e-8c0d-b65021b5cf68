export function login(username, password) {
  if (isLoggedIn()) {
    console.log('已登录');
    // 如果已经登录，直接返回
    return;
  }
  console.log('未登录，执行登录操作');

  cy.visit('https://dev.shukeyun.com/cas/login/#/login?appId=ieCx4PxQExAFrJafek9g');
  cy.get('div.white > div:nth-of-type(2) input').click();
  cy.get('div.white > div:nth-of-type(1) input').type(username);
  cy.get('div.white > div:nth-of-type(2) input').type(password);
  cy.get('div.btn').click();
  cy.visit('https://dev.shukeyun.com/data/ai-platform-frontend/#/model');
  cy.url().should('include', '/#/'); // 等页面跳转到带 tk 的地址后再继续
  // cy.location('href').should(
  //   'eq',
  //   'https://dev.shukeyun.com/data/ai-platform-frontend/#/?tk=c3091765-53c5-4157-9d3a-bb6b5e021420',
  // );
  // cy.visit('/login');
  // cy.get('input[name=username]').type(username);
  // cy.get('input[name=password]').type(password);
  // cy.get('button[type=submit]').click();
  // cy.url().should('not.include', '/login'); // 确认跳转表示登录成功
}

function isLoggedIn() {
  // 判断 localStorage 中有没有 token
  return cy.window().then((win) => {
    return !!win.localStorage.getItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN');
  });
}
