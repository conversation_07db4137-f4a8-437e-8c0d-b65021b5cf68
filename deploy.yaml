apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}
  name: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}
    spec:
      imagePullSecrets:
      - name: harborsecret
      containers:
      - image: reg.shukeyun.com:9088/{{PROJECT_NAMESPACE}}/{{PROJECT_NAME}}:{{IMAGE_TAG}}
        name: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}
        imagePullPolicy: IfNotPresent
        resources: 
          limits:
            cpu: 100m
            memory: 400Mi
          requests:
            cpu: 100m
            memory: 200Mi
status: {}
---

apiVersion: v1
kind: Service
metadata:
  namespace: {{NAMESPACE}}
  name: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}-svc
  labels:
    name: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}-svc
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
 
    name: http
    protocol: TCP
  selector:
    app: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}
 
---


apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}-ingress
  namespace: {{NAMESPACE}}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/proxy-body-size: "1024m"
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
  - host:  {{NAMESPACE}}.shukeyun.com
    http:
      paths:
      - pathType: Prefix
        path: /{{PROJECT_NAMESPACE}}/{{PROJECT_NAME}}/?(.*)
        backend:
          service:
            name: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}-svc  
            port: 
              number: 80

---

apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}}-hpa
  namespace: {{NAMESPACE}}
spec:
  maxReplicas: 10 ##资源最大副本数
  minReplicas: 1 ##资源最小副本数
  ##需要scale的target
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment ##需要伸缩的资源类型
    name:   {{PROJECT_NAMESPACE}}-{{PROJECT_NAME}} ##需要伸缩的资源名称
    ##扩容的依据
  targetCPUUtilizationPercentage: 70 ##触发伸缩的pod cpu使用率


