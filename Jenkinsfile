pipeline {
    agent any

    environment {
        REGISTRY_HOST = 'https://reg.shukeyun.com:9088/'

        KUBECONFIG = '/etc/deploy/config'
        KUBECONFIG2PROD = '/etc/deploy/kube2prod'
        KUBECONFIG2CANARY = '/etc/deploy/kube2canary'

        CI_PROJECT_NAMESPACE = 'data'
        CI_PROJECT_NAME = 'ai-platform-frontend'
        // docker构建上下文目录
        DOCKER_CONTEXT_DIR = 'docker-context'

        SONAR_TOKEN = credentials('jenkins-sonarqube')  // 引用 Jenkins 凭证
        SONAR_HOST = 'http://*************:9090'

        // GitLab 仓库地址
        GITLAB_PROJECT_ID = 654
        GITLAB_API = 'https://git.shukeyun.com/api/v4/projects'
        GITLAB_URL = 'https://git.shukeyun.com/research-and-development/data/ai-platform-frontend.git'
    }

    options {
        disableConcurrentBuilds()
    }

    stages {
        stage('Checkout') {
            steps {
                script {
                    try {
                        // sh 'env'
                        // 手动检出代码
                        updateGitlabCommitStatus(name: '拉取代码', state: 'pending')
                        def branchName = env.gitlabMergeRequestLastCommit ?: env.ManualBranchName
                        echo "拉取代码的hash或分支名 ${branchName} "
                        if (branchName) {
                            checkout([
                                $class: 'GitSCM',
                                branches: [[name: branchName]],
                                userRemoteConfigs: [[
                                    url: env.GITLAB_URL,
                                    credentialsId: 'jenkins-robot'
                                ]]
                            ])
                        }

                        updateGitlabCommitStatus(name: '拉取代码', state: 'success')
                    } catch (Exception e) {
                        updateGitlabCommitStatus(name: '拉取代码', state: 'failed')
                        throw e
                    }
                }
            }
        }

        stage('SetPanelInfo') {
            steps {
                script {
                    try {
                        // 解析git提交信息
                        parseGitLog()

                        def releaseFile = "${env.WORKSPACE}/.last_release_branch"
                        def devFile = "${env.WORKSPACE}/.last_dev_branch"

                        // hotfix分支合并master分支请求通过时，触发prod自动构建
                        env.hotfixBuild = (
                            env.gitlabSourceBranch?.startsWith('hotfix/') &&
                            env.gitlabTargetBranch == 'master' &&
                            env.gitlabMergeRequestState == 'merged')

                        if (env.hotfixBuild == 'true') {
                            env.ManualDeployEnv = 'prod'
                        }

                        wrap([$class: 'BuildUser']) {
                            // 需要构建才能部署的情况:一种是手动触发,一种是gitlab webhook触发
                            if (env.ManualBranchName || env.gitlabUserName) {
                                def starter = env.merger ?: (env.gitlabUserName ?: env.BUILD_USER)
                                def starterMsg = ''

                                if (env.merger) {
                                    starterMsg = "合并提交者: ${env.merger}"
                                }else {
                                    starterMsg = "启动者: ${starter}"
                                }

                                def shortSha = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
                                def dockerImageTag = "${env.gitlabBranch ?: env.ManualBranchName.replace('origin/', '')}-${shortSha}"

                                if (env.gitlabBranch?.startsWith('release/')) {
                                    writeFile file: releaseFile, text: dockerImageTag
                                }else if (env.gitlabBranch?.startsWith('dev')) {
                                    writeFile file: devFile, text: dockerImageTag
                                }

                                echo "构建分支: ${dockerImageTag}"
                                currentBuild.description = "${env.gitlabUserName ? '自动' : '手动'}部署: ${env.ManualDeployEnv}, 分支: ${dockerImageTag}, ${starterMsg}"
                                env.DockerImageTag = dockerImageTag.replaceAll('/', '-')
                            }
                            // 无需构建直接使用已有镜像部署的情况
                            else if (env.ManualDockerTag) {
                                def starter = 'unknown'
                                def starterMsg = ''

                                if (env.BUILD_USER) {
                                    starter = env.BUILD_USER
                                }
                                if (env.merger) {
                                    starterMsg = "合并提交者: ${env.merger}"
                                }else {
                                    starterMsg = "启动者: ${starter}"
                                }

                                def lastBranch = 'unknown'
                                if (['prod', 'canary'].contains(env.ManualDeployEnv)) {
                                    lastBranch = fileExists(releaseFile) ? readFile(releaseFile).trim() : 'unknown'
                                } else if (['dev', 'test'].contains(env.ManualDeployEnv)) {
                                    lastBranch = fileExists(devFile) ? readFile(devFile).trim() : 'unknown'
                                }

                                currentBuild.description = "手动部署: ${env.ManualDeployEnv}, 分支: ${env.ManualDockerTag ?: lastBranch}, ${starterMsg}"
                                env.DockerImageTag = env.ManualDockerTag ?: lastBranch.replaceAll('/', '-')
                            }
                        }

                        // 分支名用来生成docker tag，分支名中如果含有中文的话, 不符合docker tag命名规范，需要清洗
                        env.DockerImageTag = env.DockerImageTag
                                            .replaceAll('[^a-zA-Z0-9._-]+', '-')   // 将非法字符（如中文）替换成 -
                                            .replaceAll('-+', '-')                 // 将连续多个 - 合并为一个 -
                                            .replaceAll('^-+|-+$', '')             // 去掉开头结尾的 -
                    } catch (Exception e) {
                        echo "Error during initialization: ${e.message}"
                        error('Pipeline failed due to initialization error.')
                        throw e
                    }
                }
            }
        }

        stage('ShouldBuild') {
            steps {
                script {
                    try {
                        def devCanaryBuild = env.ManualDeployEnv in ['canary', 'dev'] && (env.gitlabUserName ? true : false)

                        // 手动可以触发任何环境构建
                        def manualBuild = env.ManualBranchName ? true : false

                        // Webhook只触发dev和canary环境,以及hotfix合并master之后的prod构建
                        def webhookBuild =  env.hotfixBuild == 'true' || devCanaryBuild

                        // 手动选择docker镜像，不执行部署
                        if (env.ManualDockerTag) {
                            env.SHOULD_BUILD = 'false'
                         }else {
                            env.SHOULD_BUILD = (manualBuild ?:  webhookBuild).toString()
                        }

                        echo "是否执行构建:  ${env.SHOULD_BUILD}"

                        if (env.SHOULD_BUILD == 'true') {
                            env.TAG_FILE_NAME = "${env.DockerImageTag}.tar.gz"
                            env.IMAGE_TAG = "${REGISTRY_HOST.replace('https://', '')}${env.CI_PROJECT_NAMESPACE}/${env.CI_PROJECT_NAME}:${env.DockerImageTag}"
                        }
                    } catch (Exception e) {
                        echo "Error during auto-build check: ${e.message}"
                        throw e
                    }
                }
            }
        }

        stage('分支名和提交信息检查') {
            when {
                expression {
                    // 满足自动构建条件时才执行
                    return env.SHOULD_BUILD == 'true'
                }
            }
            steps {
                script {
                    try {
                        // 手动检出代码
                        updateGitlabCommitStatus(name: '分支名和提交信息检查', state: 'pending')

                        sh """#!/bin/bash
                            set -e
                            source ~/.bashrc
                            nvm use 18.20.6
                            if ! command -v pnpm > /dev/null 2>&1; then
                                echo "pnpm not found, installing..."
                                npm install -g pnpm
                            else
                                echo "pnpm already installed"
                            fi

                            pnpm config set store-dir .pnpm-store
                            pnpm config set registry http://registry.npmmirror.com

                            pnpm install

                            chmod +x .husky/pre-commit .husky/commit-msg
                           .husky/pre-commit ${env.sourceBranch ?: env.branchName}
                            echo "${env.commitMessage}" > .husky/temp-commit-msg
                            .husky/commit-msg .husky/temp-commit-msg
                            rm -rf .husky/temp-commit-msg
                        """
                        updateGitlabCommitStatus(name: '分支名和提交信息检查', state: 'success')
                    } catch (Exception e) {
                        updateGitlabCommitStatus(name: '分支名和提交信息检查', state: 'failed')
                        throw e
                    }
                }
            }
        }
        stage('打包web资源') {
            when {
                expression {
                    // 满足自动构建条件时才执行
                    return env.SHOULD_BUILD == 'true'
                }
            }
            steps {
                script {
                    try {
                        updateGitlabCommitStatus(name: '打包web资源', state: 'pending')
                        sh """#!/bin/bash
                            set -e
                            source ~/.bashrc
                            nvm use 18.20.6
                            pnpm build
                            #rm -rf  *.tar.gz
                            mkdir -p ${env.DOCKER_CONTEXT_DIR}
                            rm -rf ./${env.DOCKER_CONTEXT_DIR}/*.tar.gz
                            tar czf ./${env.DOCKER_CONTEXT_DIR}/${env.TAG_FILE_NAME} dist/

                            #cat dist/index.html | grep "2025"
                        """
                        updateGitlabCommitStatus(name: '打包web资源', state: 'success')
                    } catch (Exception e) {
                        updateGitlabCommitStatus(name: '打包web资源', state: 'failed')
                        throw e
                    }
                }
            }
        }

        // stage('SonarQube Scan') {
        //     when {
        //         expression {
        //             // 满足自动构建条件时才执行
        //             return env.SHOULD_BUILD == 'true'
        //         }
        //     }

        //     steps {
        //         script {
        //             withSonarQubeEnv('SonarQube') {
        //                 sh """
        //                     bash -c "source ~/.bashrc && sonar-scanner \\
        //                     -Dsonar.projectKey=${CI_PROJECT_NAME} \\
        //                     -Dsonar.sources=. \\
        //                     -Dsonar.working.directory=.sonar \\
        //                     -Dsonar.login=$SONAR_TOKEN
        //                     "
        //                 """
        //             }
        //         }
        //     }
        // }
        // stage('质量门禁检查') {
        //     when {
        //         expression {
        //             // 满足自动构建条件时才执行
        //             return env.SHOULD_BUILD == 'true'
        //         }
        //     }
        //     steps {
        //         script {
        //             timeout(time: 10, unit: 'MINUTES') {  // 延长超时时间
        //                 def qg = waitForQualityGate()
        //                 if (qg.status != 'OK') {
        //                     // 记录详细失败信息
        //                     echo "质量门禁状态: ${qg.status}"
        //                     echo "失败原因: ${qg.statusDescription}"
        //                     error "代码质量未达标，构建终止！：${qg.status}"
        //                 }
        //             }
        //         }
        //     }
        // }

        stage('生成docker镜像') {
            when {
                expression {
                    // 满足自动构建条件时才执行
                    return env.SHOULD_BUILD == 'true'
                }
            }

            steps {
                script {
                    try {
                        updateGitlabCommitStatus(name: '生成docker镜像', state: 'pending')
                        withCredentials([
                            usernamePassword(
                                credentialsId: 'REGISTRY',
                                usernameVariable: 'REGISTRY_USERNAME',
                                passwordVariable: 'REGISTRY_PASSWORD'
                            )
                        ]) {
                            sh """
                                echo "\$REGISTRY_PASSWORD" | docker login "$REGISTRY_HOST" -u "\$REGISTRY_USERNAME" --password-stdin
                                docker build -f JenkinsDockerfile --build-arg TAR_FILE="$env.TAG_FILE_NAME" -t "$IMAGE_TAG" ./"$DOCKER_CONTEXT_DIR"
                                docker push "$IMAGE_TAG"
                            """
                        }

                        updateGitlabCommitStatus(name: '生成docker镜像', state: 'success')
                    } catch (Exception e) {
                        updateGitlabCommitStatus(name: '生成docker镜像', state: 'failed')
                        throw e
                    }
                }
            }
        }

        stage('部署docker镜像') {
            when {
                expression {
                    // 部署镜像不能为空
                    return env.DockerImageTag?.trim()
                }
            }
            steps {
                script {
                    try {
                        updateGitlabCommitStatus(name: '部署docker镜像', state: 'pending')
                        withCredentials([
                            string(credentialsId: 'kube2tke', variable: 'KUBE2TKE'),
                            string(credentialsId: 'kube2prod', variable: 'KUBE2PROD')
                        ]) {
                            sh """
                                echo "\$KUBE2TKE" | base64 -id > "$env.KUBECONFIG"
                                echo "\$KUBE2PROD" | base64 -id > "$env.KUBECONFIG2CANARY"
                                echo "\$KUBE2PROD" | base64 -id > "$env.KUBECONFIG2PROD"
                                /bin/bash -x ./jenkins-deploy.sh "$env.ManualDeployEnv" "$env.DockerImageTag"
                            """
                        }
                        updateGitlabCommitStatus(name: '部署docker镜像', state: 'success')
                } catch (Exception e) {
                        updateGitlabCommitStatus(name: '部署docker镜像', state: 'failed')
                        throw e
                    }
                }
            }
        }
    }

    post {
        success {
            echo '✅ Build and package succeeded!'
        }
        failure {
            echo '❌ Build and package failed!'
        }
        aborted {
            echo '🚫 Build and package was canceled!'
        }
        always {
            script {
                echo "发送邮件条件:  ${env.ManualDeployEnv in ['test', 'canary'] }  ${env.sourceBranch} "
                if (env.ManualDeployEnv in [ 'test', 'canary'] && env.sourceBranch) {
                    // 运行 ESLint 并生成 JSON 报告
                    timeout(time: 10, unit: 'MINUTES') {
                        sh """
                            set -e
                            bash -c "
                                source ~/.bashrc
                                nvm use 18.20.6
                                npx eslint 'src/**/*.{tsx,ts,vue}' --cache --format json --output-file eslint-report.json || true
                            "
                        """

                        // 读取 ESLint 报告文件
                        def reportFile = readFile file: 'eslint-report.json'

                        // 解析 JSON 报告
                        def report = readJSON text: reportFile

                        // 初始化计数器
                        int totalErrors = 0
                        int totalWarnings = 0

                        // 遍历报告中的每个文件
                        report.each { fileReport ->
                            fileReport.messages.each { message ->
                                if (message.severity == 2) {
                                    totalErrors++
                                } else if (message.severity == 1) {
                                    totalWarnings++
                                }
                            }
                        }
                        env.totalErrors = totalErrors
                        env.totalWarnings = totalWarnings

                        // 输出统计结果
                        echo "Jenkins构建地址: ${env.BUILD_URL}"
                        echo "提交地址: ${env.commitUrl}"
                        echo "源分支: ${env.sourceBranch}"
                        echo "目标分支: ${env.branchName}"
                        echo "提交信息: ${env.commitMessage}"
                        echo "提交作者: ${env.commitAuthorName}"
                        echo "ESLint 总错误数: ${env.totalErrors}"
                        echo "ESLint 总警告数: ${env.totalWarnings}"
                    }

                    // timeout(time: 10, unit: 'MINUTES') {
                    //     sh '''
                    //         set -e
                    //         bash -c "
                    //             source ~/.bashrc
                    //             nvm use 18.20.6
                    //             pnpm run cypress:online
                    //         "
                    //     '''
                    //     def summary = readJSON file: 'coverage/e2e/coverage-summary.json'
                    //     def total = summary.total

                    //     env.COVERAGE_LINES     = "${total.lines.pct}"
                    //     env.COVERAGE_BRANCHES  = "${total.branches.pct}"
                    //     env.COVERAGE_FUNCTIONS = "${total.functions.pct}"
                    //     env.COVERAGE_STATEMENTS = "${total.statements.pct}"
                    // }

                    withSonarQubeEnv('SonarQube') {
                        sh """
                            bash -c "source ~/.bashrc && sonar-scanner \\
                            -Dsonar.projectKey=${CI_PROJECT_NAME} \\
                            -Dsonar.sources=. \\
                            -Dsonar.working.directory=.sonar \\
                            -Dsonar.login=$SONAR_TOKEN"
                        """
                    }

                    // 调用SonarQube API获取报告数据
                    def sonarData = getSonarReport()

                    def reportContent = """
                        <p>Jenkins+ESLint+Sonar扫描结果</p>
                        <p>项目：${env.JOB_NAME}</p>
                        <p>状态：<strong>${currentBuild.currentResult}</strong></p>
                        <p>详情：<a href="${env.BUILD_URL}">${env.BUILD_URL}</a></p>
                        <p>提交地址: <a href="${env.commitUrl}">${env.commitUrl}</a></p>
                        <p>提交ID: ${env.commitHash}</p>
                        <p>源分支: ${env.sourceBranch}</p>
                        <p>目标分支: ${env.branchName}</p>
                        <p>提交信息: ${env.commitMessage}</p>
                        <p>提交作者: ${env.commitAuthorName}</p>
                        <p>ESLint 总错误数: ${env.totalErrors ?: 0}</p>
                        <p>ESLint 总警告数: ${env.totalWarnings ?: 0}</p>

                        <p>详细信息: <a href="${SONAR_HOST}/dashboard?id=${CI_PROJECT_NAME}">SonarQube链接</a></p>
                        <table border="1" cellpadding="5">
                            <tr><th>指标</th><th>结果</th></tr>
                            <tr><td>可靠性问题（未解决）</td><td>${sonarData.software_quality_reliability_issues}</td></tr>
                            <tr><td>可维护性问题（未解决）</td><td>${sonarData.software_quality_maintainability_issues}</td></tr>
                            <tr><td>安全问题（未解决）</td><td>${sonarData.vulnerabilities}</td></tr>
                            <tr><td>安全热点（未解决）</td><td>${sonarData.security_hotspots}</td></tr>
                            <tr><td>覆盖率</td><td>${sonarData.coverage}%</td></tr>
                            <tr><td>重复代码率</td><td>${sonarData.duplication}%</td></tr>
                            <tr><td>可靠性评级</td><td>${sonarData.reliability}</td></tr>
                            <tr><td>可维护性评级</td><td>${sonarData.maintainability}</td></tr>
                            <tr><td>安全漏洞评级</td><td>${sonarData.security}</td></tr>
                        </table>

                    """.stripIndent()

                        // <p>Cypress 行覆盖率: ${env.COVERAGE_LINES ?: 0}</p>
                        // <p>Cypress 分支覆盖率: ${env.COVERAGE_BRANCHES ?: 0}</p>
                        // <p>Cypress 函数覆盖率: ${env.COVERAGE_FUNCTIONS ?: 0}</p>
                        // <p>Cypress 语句覆盖率: ${env.COVERAGE_STATEMENTS ?: 0}</p>

                    emailext(
                        subject: "项目:${env.JOB_NAME}--环境:${env.ManualDeployEnv} -- 构建结果：${currentBuild.currentResult}",
                        body: reportContent,
                        // to: '<EMAIL>',
                        to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>',
                        from: '<EMAIL>',
                        mimeType: 'text/html'
                    )

                    withCredentials([string(credentialsId: 'jenkins-ci', variable: 'MergedNotesToken')]) {
                        writeReportToGitLab(env.GITLAB_API, env.GITLAB_PROJECT_ID, env.sourceBranch, MergedNotesToken, reportContent)
                    }
                }else {
                    echo " ${env.ManualDeployEnv} 环境 不在允许发送邮件的列表中，跳过发送邮件。"
                }
            }
        }
    }
}

def getSonarReport() {
    def sonarData = [:]
    withCredentials([string(credentialsId: 'sonar-token', variable: 'SONAR_TOKEN')]) {
        def metrics = 'reliability_rating,sqale_rating,coverage,duplicated_lines_density,security_hotspots,security_rating,software_quality_maintainability_issues,software_quality_reliability_issues,vulnerabilities'
        def response = sh(
            script: """
                curl -s -u '${SONAR_TOKEN}:' \
                "${SONAR_HOST}/api/measures/component?component=${CI_PROJECT_NAME}&metricKeys=${metrics}"
            """,
            returnStdout: true
        )

        def json = readJSON text: response
        if (json.errors) {
            error "SonarQube API错误: ${json.errors[0].msg}"
        }

        // 打印原始 JSON
        // echo '原始 JSON 响应：'
        // echo groovy.json.JsonOutput.prettyPrint(groovy.json.JsonOutput.toJson(json))

        // 初始化默认值
        sonarData = [
            reliability: 'N/A',
            maintainability: 'N/A',
            coverage: 'N/A',
            duplication: 'N/A',
            security: 'N/A',
            security_hotspots: 'N/A',
            software_quality_reliability_issues: '0',
            software_quality_maintainability_issues: '0',
            vulnerabilities: '0'
        ]

        // 遍历指标并赋值
        json.component.measures.each { measure ->
            switch (measure.metric) {
                case 'reliability_rating':
                    sonarData.reliability = convertRatingToLetter(measure.value?.toDouble())
                    break
                case 'sqale_rating':
                    sonarData.maintainability = convertRatingToLetter(measure.value?.toDouble())
                    break
                case 'coverage':
                    sonarData.coverage = measure.value ?: '0'
                    break
                case 'duplicated_lines_density':
                    sonarData.duplication = measure.value ?: '0'
                    break
                case 'security_rating':
                    sonarData.security = convertRatingToLetter(measure.value?.toDouble())
                    break
                case 'security_hotspots':
                    sonarData.security_hotspots = measure.value ?: '0'
                    break
                case 'software_quality_reliability_issues':
                    sonarData.software_quality_reliability_issues = measure.value ?: '0'
                    break
                case 'software_quality_maintainability_issues':
                    sonarData.software_quality_maintainability_issues = measure.value ?: '0'
                    break
                case 'vulnerabilities':
                    sonarData.vulnerabilities = measure.value ?: '0'
                    break
            }
        }
    }
    return sonarData
}
def convertRatingToLetter(Double value) {
    switch (value) {
        case 1.0: return 'A'
        case 2.0: return 'B'
        case 3.0: return 'C'
        case 4.0: return 'D'
        case 5.0: return 'E'
        default:  return 'N/A'  // 处理异常值
    }
}

// 将代码质量摘写入到gitlab的merge request的notes中
def writeReportToGitLab(host, projectId, sourceBranch, token, reportContent) {
    def response = sh(
        script: """
            curl --silent --header "PRIVATE-TOKEN: ${token}" \
            "${host}/${projectId}/merge_requests?state=merged&source_branch=${URLEncoder.encode(sourceBranch, 'UTF-8')}"
        """,
        returnStdout: true
    ).trim()

    def json = readJSON text: response
    def mergeId = json[0]?.iid

    echo "合并请求的ID: ${mergeId}"

    if (mergeId) {
        def jsonBody = groovy.json.JsonOutput.toJson([ body: reportContent ])

        sh(script: """
            curl --request POST "${host}/${projectId}/merge_requests/${mergeId}/notes" \
                --header "PRIVATE-TOKEN: ${token}" \
                --header "Content-Type: application/json" \
                --data '${jsonBody}' > /dev/null 2>&1
        """)
    }
}

def parseGitLog() {
    // 获取 HEAD commit 信息
    handleGitLog("git log -1 --pretty=format:'%an|%s' | tail -n 1")

    // 如果提交作者是Jenkins-ci，则上一个提交信息才是人工提交的
    if (env.commitAuthorName == 'Jenkins-ci') {
        handleGitLog("git log -2 --pretty=format:'%an|%s' | tail -n 1")
    }

    env.branchName = (env.gitlabBranch ?: env.ManualBranchName).replace('origin/', '')
    env.commitHash = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
    env.commitUrl = "${env.GITLAB_URL.replace('.git', '')}/-/commit/${env.commitHash}"

    // 判断是否是合并提交
    if (env.commitMessage.contains('Merge branch')) {
        echo '检测到合并提交，解析源分支最新提交信息...'

        // 获取合并的源分支名
        def mergedBranchCommand = '''
            git log --merges --grep="^Merge branch" -1 --pretty=format:"%s" | sed -E "s/^Merge branch '([^']+)'.*/\\1/"
        '''.trim()
        env.sourceBranch = sh(script: mergedBranchCommand, returnStdout: true).trim()

        withCredentials([usernamePassword(
            credentialsId: 'jenkins-robot',
            usernameVariable: 'GIT_USERNAME',
            passwordVariable: 'GIT_TOKEN'
        )]) {
            // URL encode 用户名和密码
            def encodedUser = URLEncoder.encode(GIT_USERNAME, 'UTF-8')
            def encodedPass = URLEncoder.encode(GIT_TOKEN, 'UTF-8')

            sh """
                set -e
                git remote set-url origin https://${encodedUser}:${encodedPass}@${env.GITLAB_URL.replace('https://', '')}
                git fetch origin ${env.sourceBranch}
            """
        }

        // 取原分支最新一条合并信息
        handleGitLog("git log origin/${env.sourceBranch} -1 --pretty=format:'%an|%s' | tail -n 1")

    // echo "合并的作者名: ${env.commitAuthorName}"
    // echo "合并的源分支名: ${env.sourceBranch}"
    // echo "获取的合并信息 ${env.commitMessage}"
    }
}

def handleGitLog(gitQueryCmd) {
    def logLine = sh(script: gitQueryCmd, returnStdout: true).trim()
    // logLine 形如 '张三|提交说明'
    def parts = logLine.tokenize('|')
    env.commitAuthorName = parts[0]
    env.merger = parts[0]
    env.commitMessage = parts[1]
}
