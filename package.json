{"name": "ai_platform_frontend", "version": "0.0.6", "private": true, "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "vite build --mode dev", "build:dev": "vite build --mode dev", "build:test": "vite build --mode test", "build:canary": "vite build --mode canary", "build:master": "vite build --mode prod", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:report": "eslint src --config eslint.config.ts -f json -o eslint-report.json", "lint": "run-s lint:*", "lint:eslint": "eslint src/**/*.{ts,tsx,vue} --fix", "lint:prettier": "prettier --check ./", "lint:style": "stylelint \"src/**/*.{vue,css,less}\" --fix", "format:fix": "prettier --write ./", "prepare": "husky", "test:unit": "vitest", "test:preview": "vite preview --outDir html", "test:coverage": "vitest run --coverage", "test:e2e": "cross-env COVERAGE=true start-server-and-test dev 5173 cypress:run", "cypress:run": "npx cypress run --browser electron", "cypress:online": "cross-env COVERAGE=true CYPRESS_BASE_URL=https://dev.shukeyun.com  npx cypress run --browser electron"}, "dependencies": {"@ant-design/icons-vue": "7.0.1", "@microsoft/fetch-event-source": "^2.0.1", "@originjs/vite-plugin-commonjs": "1.0.3", "ace-builds": "1.39.0", "ant-design-vue": "4.2.6", "axios": "1.7.9", "dayjs": "1.11.13", "echarts": "5.6.0", "js-yaml": "^4.1.0", "json-editor-vue3": "1.1.1", "lodash-es": "4.17.21", "markdown-it": "^14.1.0", "marked": "^15.0.12", "md-editor-v3": "5.4.5", "pinia": "3.0.1", "recorder-core": "^1.3.25011100", "uuid": "11.1.0", "vue": "3.5.13", "vue-router": "4.5.0", "vue3-ace-editor": "2.2.4"}, "devDependencies": {"@commitlint/cli": "^17.7.0", "@commitlint/config-conventional": "^17.7.0", "@cspell/eslint-plugin": "^8.19.2", "@cypress/code-coverage": "^3.14.4", "@tsconfig/node22": "22.0.0", "@types/jsdom": "21.1.7", "@types/node": "22.13.4", "@types/qs": "6.9.18", "@vitejs/plugin-vue": "5.2.1", "@vitejs/plugin-vue-jsx": "4.1.1", "@vitest/coverage-istanbul": "^3.2.3", "@vitest/coverage-v8": "3.0.5", "@vitest/eslint-plugin": "1.1.31", "@vitest/ui": "3.0.5", "@vue/eslint-config-prettier": "10.2.0", "@vue/eslint-config-typescript": "14.4.0", "@vue/test-utils": "2.4.6", "@vue/tsconfig": "0.7.0", "code-inspector-plugin": "0.20.9", "conventional-changelog-custom-config": "^0.3.1", "cross-env": "^7.0.3", "cypress": "^14.4.1", "eslint": "9.20.1", "eslint-plugin-vue": "9.32.0", "husky": "9.1.7", "jiti": "2.4.2", "jsdom": "26.0.0", "less": "4.2.2", "less-loader": "12.2.0", "npm-run-all2": "7.0.2", "nyc": "^17.1.0", "postcss": "8.5.3", "postcss-html": "1.8.0", "postcss-less": "6.0.0", "prettier": "3.5.1", "qs": "6.14.0", "start-server-and-test": "^2.0.12", "stylelint": "16.15.0", "stylelint-config-recess-order": "6.0.0", "stylelint-config-recommended-vue": "1.6.0", "stylelint-config-standard": "37.0.0", "stylelint-order": "6.0.4", "terser": "5.16.1", "typescript": "5.7.3", "unocss": "66.0.0", "vite": "6.1.0", "vite-plugin-istanbul": "^7.0.0", "vite-plugin-vue-devtools": "7.7.2", "vitest": "3.0.5", "vue-tsc": "2.2.2"}}